using SmaTrendFollower.Services;
using SmaTrendFollower.Models;
using Alpaca.Markets;
using FluentAssertions;
using Microsoft.Extensions.Logging;
using Moq;
using Xunit;

namespace SmaTrendFollower.Tests.Services;

/// <summary>
/// Comprehensive tests for EnhancedSignalGenerator
/// Tests momentum filtering, volatility filtering, parallel processing, and position sizing
/// </summary>
public class EnhancedSignalGeneratorTests : IDisposable
{
    private readonly Mock<IMarketDataService> _mockMarketDataService;
    private readonly Mock<IUniverseProvider> _mockUniverseProvider;
    private readonly Mock<ILiveStateStore> _mockLiveStateStore;
    private readonly Mock<IMomentumFilter> _mockMomentumFilter;
    private readonly Mock<IVolatilityFilter> _mockVolatilityFilter;
    private readonly Mock<IPositionSizer> _mockPositionSizer;
    private readonly Mock<ILogger<EnhancedSignalGenerator>> _mockLogger;
    private readonly EnhancedSignalGenerator _generator;

    public EnhancedSignalGeneratorTests()
    {
        _mockMarketDataService = new Mock<IMarketDataService>();
        _mockUniverseProvider = new Mock<IUniverseProvider>();
        _mockLiveStateStore = new Mock<ILiveStateStore>();
        _mockMomentumFilter = new Mock<IMomentumFilter>();
        _mockVolatilityFilter = new Mock<IVolatilityFilter>();
        _mockPositionSizer = new Mock<IPositionSizer>();
        _mockLogger = new Mock<ILogger<EnhancedSignalGenerator>>();

        _generator = new EnhancedSignalGenerator(
            _mockMarketDataService.Object,
            _mockUniverseProvider.Object,
            _mockLiveStateStore.Object,
            _mockMomentumFilter.Object,
            _mockVolatilityFilter.Object,
            _mockPositionSizer.Object,
            _mockLogger.Object);
    }

    [Fact]
    public async Task RunAsync_WithValidSymbols_ShouldReturnFilteredSignals()
    {
        // Arrange
        var symbols = new[] { "AAPL", "MSFT", "GOOGL" };
        var topN = 2;

        SetupSuccessfulDataFetch(symbols);
        SetupSuccessfulFiltering();
        SetupSuccessfulPositionSizing();

        // Act
        var signals = await _generator.RunAsync(topN);
        var signalList = signals.ToList();

        // Assert
        signalList.Should().HaveCount(topN);
        signalList.Should().OnlyContain(s => s.Price > 0 && s.Atr > 0);
        _mockUniverseProvider.Verify(x => x.GetSymbolsAsync(), Times.Once);
    }

    [Fact]
    public async Task RunAsync_WithMomentumFiltering_ShouldApplyMomentumFilter()
    {
        // Arrange
        var symbols = new[] { "STRONG_MOMENTUM", "WEAK_MOMENTUM" };
        SetupSuccessfulDataFetch(symbols);

        var strongMomentum = new MomentumAnalysis(
            Symbol: "STRONG_MOMENTUM",
            MomentumScore: 0.85m,
            TrendStrength: TrendStrength.Strong,
            IsAccelerating: true,
            RelativeStrength: 0.90m
        );

        var weakMomentum = new MomentumAnalysis(
            Symbol: "WEAK_MOMENTUM",
            MomentumScore: 0.25m,
            TrendStrength: TrendStrength.Weak,
            IsAccelerating: false,
            RelativeStrength: 0.30m
        );

        _mockMomentumFilter.Setup(x => x.AnalyzeMomentum("STRONG_MOMENTUM", It.IsAny<List<IBar>>()))
            .Returns(strongMomentum);
        _mockMomentumFilter.Setup(x => x.AnalyzeMomentum("WEAK_MOMENTUM", It.IsAny<List<IBar>>()))
            .Returns(weakMomentum);

        SetupSuccessfulVolatilityFiltering();
        SetupSuccessfulPositionSizing();

        // Act
        var signals = await _generator.RunAsync(10);
        var signalList = signals.ToList();

        // Assert
        _mockMomentumFilter.Verify(x => x.AnalyzeMomentum(It.IsAny<string>(), It.IsAny<List<IBar>>()), Times.AtLeast(2));
        signalList.Should().Contain(s => s.Symbol == "STRONG_MOMENTUM");
        // Weak momentum should be filtered out or ranked lower
    }

    [Fact]
    public async Task RunAsync_WithVolatilityFiltering_ShouldApplyVolatilityFilter()
    {
        // Arrange
        var symbols = new[] { "LOW_VOL", "HIGH_VOL" };
        SetupSuccessfulDataFetch(symbols);
        SetupSuccessfulMomentumFiltering();

        var lowVolAnalysis = new SymbolVolatilityAnalysis(
            Symbol: "LOW_VOL",
            CurrentVolatility: 0.15m,
            VolatilityPercentile: 25m,
            VolatilityRegime: VolatilityRegime.Low,
            IsVolatilityExpanding: false,
            RiskAdjustedReturn: 0.80m
        );

        var highVolAnalysis = new SymbolVolatilityAnalysis(
            Symbol: "HIGH_VOL",
            CurrentVolatility: 0.45m,
            VolatilityPercentile: 95m,
            VolatilityRegime: VolatilityRegime.High,
            IsVolatilityExpanding: true,
            RiskAdjustedReturn: 0.20m
        );

        _mockVolatilityFilter.Setup(x => x.AnalyzeSymbolVolatility("LOW_VOL", It.IsAny<List<IBar>>()))
            .Returns(lowVolAnalysis);
        _mockVolatilityFilter.Setup(x => x.AnalyzeSymbolVolatility("HIGH_VOL", It.IsAny<List<IBar>>()))
            .Returns(highVolAnalysis);

        SetupSuccessfulPositionSizing();

        // Act
        var signals = await _generator.RunAsync(10);
        var signalList = signals.ToList();

        // Assert
        _mockVolatilityFilter.Verify(x => x.AnalyzeSymbolVolatility(It.IsAny<string>(), It.IsAny<List<IBar>>()), Times.AtLeast(2));
        signalList.Should().Contain(s => s.Symbol == "LOW_VOL");
        // High volatility should be filtered out or ranked lower
    }

    [Fact]
    public async Task RunAsync_WithPositionSizing_ShouldCalculatePositionSizes()
    {
        // Arrange
        var symbols = new[] { "AAPL", "MSFT" };
        SetupSuccessfulDataFetch(symbols);
        SetupSuccessfulFiltering();

        var positionSizing = new DynamicPositionSizing(
            Shares: 100m,
            DollarAmount: 15000m,
            ConfidenceScore: 0.85m,
            RiskScore: 0.25m,
            MaxPositionSize: 20000m
        );

        _mockPositionSizer.Setup(x => x.CalculatePositionSizeAsync(It.IsAny<EnhancedTradingSignalV2>()))
            .ReturnsAsync(positionSizing);

        // Act
        var signals = await _generator.RunAsync(10);
        var signalList = signals.ToList();

        // Assert
        _mockPositionSizer.Verify(x => x.CalculatePositionSizeAsync(It.IsAny<EnhancedTradingSignalV2>()), Times.AtLeast(2));
        signalList.Should().OnlyContain(s => s.Price > 0);
    }

    [Fact]
    public async Task RunAsync_WithParallelProcessing_ShouldProcessSymbolsConcurrently()
    {
        // Arrange
        var symbols = Enumerable.Range(1, 20).Select(i => $"SYMBOL{i}").ToArray();
        SetupSuccessfulDataFetch(symbols);
        SetupSuccessfulFiltering();
        SetupSuccessfulPositionSizing();

        var processingTimes = new List<DateTime>();
        _mockMarketDataService.Setup(x => x.GetStockBarsAsync(It.IsAny<string>(), It.IsAny<DateTime>(), It.IsAny<DateTime>(), It.IsAny<CancellationToken>()))
            .Callback(() => processingTimes.Add(DateTime.UtcNow))
            .ReturnsAsync(CreateMockBarsResponse());

        // Act
        var startTime = DateTime.UtcNow;
        var signals = await _generator.RunAsync(10);
        var endTime = DateTime.UtcNow;

        // Assert
        var totalDuration = endTime - startTime;
        // Parallel processing should be faster than sequential
        totalDuration.Should().BeLessThan(TimeSpan.FromSeconds(10)); // Should complete quickly with parallel processing
        processingTimes.Should().HaveCount(symbols.Length);
    }

    [Fact]
    public async Task RunAsync_WithBasicTrendFilter_ShouldFilterByTrend()
    {
        // Arrange
        var symbols = new[] { "UPTREND", "DOWNTREND" };
        
        // Setup uptrend symbol (price > SMA50 > SMA200)
        var uptrendBars = CreateBarsWithTrend("UPTREND", currentPrice: 110m, sma50: 105m, sma200: 100m);
        var downtrendBars = CreateBarsWithTrend("DOWNTREND", currentPrice: 90m, sma50: 95m, sma200: 100m);

        _mockUniverseProvider.Setup(x => x.GetSymbolsAsync()).ReturnsAsync(symbols);
        _mockMarketDataService.Setup(x => x.GetStockBarsAsync("UPTREND", It.IsAny<DateTime>(), It.IsAny<DateTime>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(uptrendBars);
        _mockMarketDataService.Setup(x => x.GetStockBarsAsync("DOWNTREND", It.IsAny<DateTime>(), It.IsAny<DateTime>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(downtrendBars);

        SetupSuccessfulFiltering();
        SetupSuccessfulPositionSizing();

        // Act
        var signals = await _generator.RunAsync(10);
        var signalList = signals.ToList();

        // Assert
        signalList.Should().Contain(s => s.Symbol == "UPTREND");
        signalList.Should().NotContain(s => s.Symbol == "DOWNTREND");
    }

    [Fact]
    public async Task RunAsync_WithException_ShouldReturnEmptyList()
    {
        // Arrange
        _mockUniverseProvider.Setup(x => x.GetSymbolsAsync()).ThrowsAsync(new Exception("Universe provider error"));

        // Act
        var signals = await _generator.RunAsync(10);

        // Assert
        signals.Should().BeEmpty();
    }

    [Fact]
    public async Task RunAsync_WithDataFetchFailure_ShouldSkipFailedSymbols()
    {
        // Arrange
        var symbols = new[] { "SUCCESS", "FAILURE" };
        _mockUniverseProvider.Setup(x => x.GetSymbolsAsync()).ReturnsAsync(symbols);
        
        _mockMarketDataService.Setup(x => x.GetStockBarsAsync("SUCCESS", It.IsAny<DateTime>(), It.IsAny<DateTime>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(CreateMockBarsResponse());
        _mockMarketDataService.Setup(x => x.GetStockBarsAsync("FAILURE", It.IsAny<DateTime>(), It.IsAny<DateTime>(), It.IsAny<CancellationToken>()))
            .ThrowsAsync(new Exception("Data fetch failed"));

        SetupSuccessfulFiltering();
        SetupSuccessfulPositionSizing();

        // Act
        var signals = await _generator.RunAsync(10);
        var signalList = signals.ToList();

        // Assert
        signalList.Should().Contain(s => s.Symbol == "SUCCESS");
        signalList.Should().NotContain(s => s.Symbol == "FAILURE");
    }

    [Fact]
    public async Task RunAsync_ShouldRankByConfidenceAndReturn()
    {
        // Arrange
        var symbols = new[] { "HIGH_CONF", "LOW_CONF", "MED_CONF" };
        SetupSuccessfulDataFetch(symbols);
        SetupSuccessfulFiltering();

        var highConfSizing = new DynamicPositionSizing(100m, 15000m, 0.95m, 0.15m, 20000m);
        var medConfSizing = new DynamicPositionSizing(100m, 15000m, 0.75m, 0.25m, 20000m);
        var lowConfSizing = new DynamicPositionSizing(100m, 15000m, 0.45m, 0.35m, 20000m);

        _mockPositionSizer.Setup(x => x.CalculatePositionSizeAsync(It.Is<EnhancedTradingSignalV2>(s => s.Symbol == "HIGH_CONF")))
            .ReturnsAsync(highConfSizing);
        _mockPositionSizer.Setup(x => x.CalculatePositionSizeAsync(It.Is<EnhancedTradingSignalV2>(s => s.Symbol == "MED_CONF")))
            .ReturnsAsync(medConfSizing);
        _mockPositionSizer.Setup(x => x.CalculatePositionSizeAsync(It.Is<EnhancedTradingSignalV2>(s => s.Symbol == "LOW_CONF")))
            .ReturnsAsync(lowConfSizing);

        // Act
        var signals = await _generator.RunAsync(2); // Top 2 only
        var signalList = signals.ToList();

        // Assert
        signalList.Should().HaveCount(2);
        signalList.First().Symbol.Should().Be("HIGH_CONF"); // Highest confidence first
        signalList.Should().NotContain(s => s.Symbol == "LOW_CONF"); // Lowest confidence filtered out
    }

    private void SetupSuccessfulDataFetch(string[] symbols)
    {
        _mockUniverseProvider.Setup(x => x.GetSymbolsAsync()).ReturnsAsync(symbols);
        
        foreach (var symbol in symbols)
        {
            _mockMarketDataService.Setup(x => x.GetStockBarsAsync(symbol, It.IsAny<DateTime>(), It.IsAny<DateTime>(), It.IsAny<CancellationToken>()))
                .ReturnsAsync(CreateMockBarsResponse());
        }
    }

    private void SetupSuccessfulFiltering()
    {
        SetupSuccessfulMomentumFiltering();
        SetupSuccessfulVolatilityFiltering();
    }

    private void SetupSuccessfulMomentumFiltering()
    {
        var defaultMomentum = new MomentumAnalysis("", 0.75m, TrendStrength.Strong, true, 0.80m);
        _mockMomentumFilter.Setup(x => x.AnalyzeMomentum(It.IsAny<string>(), It.IsAny<List<IBar>>()))
            .Returns(defaultMomentum);
    }

    private void SetupSuccessfulVolatilityFiltering()
    {
        var defaultVolatility = new SymbolVolatilityAnalysis("", 0.20m, 50m, VolatilityRegime.Normal, false, 0.70m);
        _mockVolatilityFilter.Setup(x => x.AnalyzeSymbolVolatility(It.IsAny<string>(), It.IsAny<List<IBar>>()))
            .Returns(defaultVolatility);
    }

    private void SetupSuccessfulPositionSizing()
    {
        var defaultSizing = new DynamicPositionSizing(100m, 15000m, 0.80m, 0.20m, 20000m);
        _mockPositionSizer.Setup(x => x.CalculatePositionSizeAsync(It.IsAny<EnhancedTradingSignalV2>()))
            .ReturnsAsync(defaultSizing);
    }

    private IPage<IBar> CreateMockBarsResponse()
    {
        var mockBars = new List<IBar>();
        for (int i = 0; i < 250; i++)
        {
            var mockBar = Mock.Of<IBar>(b => 
                b.Close == 100m + i * 0.1m &&
                b.High == 101m + i * 0.1m &&
                b.Low == 99m + i * 0.1m &&
                b.Open == 100m + i * 0.1m &&
                b.TimeUtc == DateTime.UtcNow.AddDays(-250 + i));
            mockBars.Add(mockBar);
        }

        var mockResponse = Mock.Of<IPage<IBar>>(r => r.Items == mockBars);
        return mockResponse;
    }

    private IPage<IBar> CreateBarsWithTrend(string symbol, decimal currentPrice, decimal sma50, decimal sma200)
    {
        var mockBars = new List<IBar>();
        for (int i = 0; i < 250; i++)
        {
            var price = i == 249 ? currentPrice : 100m + i * 0.1m; // Last bar has the specified current price
            var mockBar = Mock.Of<IBar>(b => 
                b.Close == price &&
                b.High == price + 1m &&
                b.Low == price - 1m &&
                b.Open == price &&
                b.TimeUtc == DateTime.UtcNow.AddDays(-250 + i));
            mockBars.Add(mockBar);
        }

        var mockResponse = Mock.Of<IPage<IBar>>(r => r.Items == mockBars);
        return mockResponse;
    }

    public void Dispose()
    {
        _generator?.Dispose();
    }
}
